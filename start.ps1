# uyou-todo-electron 启动脚本
# 设置环境变量并启动项目

Write-Host "正在启动 uyou-todo-electron..." -ForegroundColor Green

# 设置 PNPM_HOME 环境变量
$env:PNPM_HOME = "C:\Users\<USER>\AppData\Local\pnpm"
$env:PATH = "$env:PNPM_HOME;$env:PATH"

# 进入项目目录
Set-Location "e:\utodo"

Write-Host "环境变量已设置" -ForegroundColor Yellow
Write-Host "PNPM_HOME: $env:PNPM_HOME" -ForegroundColor Cyan

# 启动项目
Write-Host "正在启动开发服务器..." -ForegroundColor Green
& "C:\Users\<USER>\AppData\Local\pnpm\nr.CMD" electron:servewin
