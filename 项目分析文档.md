# uyou-todo-electron 项目分析文档

## 项目概述

**uyou-todo-electron** 是一个基于 Electron 和 Vue 3 开发的跨平台桌面待办事项（ToDo）应用程序。该项目采用现代化的前端技术栈，提供了丰富的功能和优秀的用户体验。

## 主要功能

### 1. 核心待办事项功能
- **添加/删除/编辑** 待办事项
- **完成状态管理** - 标记任务完成/未完成
- **星标功能** - 重要任务加星标记
- **分类管理** - 自定义分类组织任务
- **时间提醒** - 设置任务提醒时间
- **搜索功能** - 快速查找任务

### 2. 界面模式
- **标准模式** - 传统的待办事项列表界面
- **便签纸模式** - 类似便签纸的界面风格
- **简易模式** - 精简的界面布局

### 3. 个性化设置
- **多语言支持** - 中文、英文、日文、西班牙文等
- **主题切换** - 深色/浅色主题，跟随系统
- **自定义字体** - 支持导入自定义字体文件
- **窗口效果** - 毛玻璃效果、Mica效果（Windows 11）
- **启动加密** - 应用启动密码保护

### 4. 数据管理
- **本地存储** - 数据存储在本地
- **备份导入导出** - 支持数据备份和恢复
- **账号同步** - 支持在线账号同步（部分功能）

## 使用的技术栈

### 前端技术
- **Vue 3** - 主要前端框架
- **Vue Router** - 路由管理
- **Vue I18n** - 国际化
- **Element Plus** - UI组件库
- **UnoCSS** - 原子化CSS框架
- **TypeScript** - 类型安全
- **Sass** - CSS预处理器

### 桌面应用技术
- **Electron** - 跨平台桌面应用框架
- **mica-electron** - Windows 11 Mica效果支持
- **electron-store** - 数据持久化存储

### 构建工具
- **Vite** - 前端构建工具
- **Rolldown** - Electron主进程构建
- **electron-builder** - 应用打包工具
- **pnpm** - 包管理器

### 开发工具
- **ESLint** - 代码规范检查
- **Vue TSC** - Vue TypeScript编译
- **Vue Vine** - Vue组件开发增强

## 项目结构

```
uyou-todo-electron/
├── src/                    # Vue前端代码
│   ├── components/         # Vue组件
│   │   ├── List/          # 待办列表组件
│   │   ├── TabBar/        # 标签栏
│   │   ├── TitleBar/      # 标题栏
│   │   ├── SettingList/   # 设置列表
│   │   └── Alert/         # 弹窗组件
│   ├── pages/             # 页面组件
│   │   ├── Home.vine.ts   # 主页面
│   │   ├── NoteUI.vine.ts # 便签纸模式
│   │   └── Settings/      # 设置相关页面
│   ├── i18n/              # 国际化
│   ├── interface/         # TypeScript接口
│   ├── util/              # 工具函数
│   ├── styles/            # 样式文件
│   ├── App.vue            # 应用入口组件
│   ├── main.ts            # Vue应用入口
│   └── router.ts          # 路由配置
├── electron/              # Electron主进程
│   ├── main.ts            # 主进程入口
│   ├── preload.ts         # 预加载脚本
│   ├── pages/             # 子窗口页面
│   ├── store/             # 数据存储
│   ├── i18n/              # 主进程国际化
│   └── menu.ts            # 菜单配置
├── public/                # 静态资源
├── demo/                  # 演示图片
├── package.json           # 项目配置
├── vite.config.mts        # Vite配置
├── uno.config.ts          # UnoCSS配置
└── tsconfig.json          # TypeScript配置
```

## 核心功能代码位置

### 1. 待办事项核心功能
- **数据模型定义**: `src/interface/ITodoListArray.ts`
- **主列表组件**: `src/components/List/List.vue`
- **单个待办项**: `src/components/List/Item/Item.vue`
- **添加待办项**: `src/components/List/AddItem/AddItem.vue`
- **本地存储**: `src/util/localStorage.ts`

### 2. 主界面和路由
- **应用入口**: `src/App.vue`
- **路由配置**: `src/router.ts`
- **主页面**: `src/pages/Home.vine.ts`
- **便签纸模式**: `src/pages/NoteUI.vine.ts`

### 3. 设置功能
- **设置页面**: `src/pages/Settings/Setting.vue`
- **语言设置**: `src/pages/Settings/LangSet.vue`
- **字体设置**: `src/pages/Settings/Vip/FontSet.vue`
- **模式选择**: `src/pages/Settings/Mode.vue`

### 4. Electron桌面功能
- **主进程**: `electron/main.ts`
- **窗口管理**: `electron/store/windowSizeStore.ts`
- **系统托盘**: `electron/main.ts` (第296-306行)
- **菜单配置**: `electron/menu.ts`

### 5. 国际化
- **中文语言包**: `src/i18n/zh-cn.json`
- **英文语言包**: `src/i18n/en.json`
- **国际化配置**: `src/i18n/index.ts`

### 6. 样式和主题
- **主样式**: `src/styles/main.scss`
- **UnoCSS配置**: `uno.config.ts`
- **主题切换**: `src/App.vue` (第157-170行)

## 特色功能实现

1. **毛玻璃效果**: 通过 `mica-electron` 库实现Windows 11的Mica效果
2. **多模式界面**: 支持标准模式和便签纸模式的切换
3. **启动加密**: `src/components/OpenPass/` 目录下的密码保护功能
4. **时间提醒**: `src/components/List/Item/setTime.ts` 实现任务提醒
5. **数据备份**: `src/pages/Settings/ToDoBackup.vine.ts` 实现导入导出功能

## 开发环境配置

### 环境要求
- Node.js 20.14.0+
- pnpm 9.0.6+
- Git

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/tonylu110/uyou-todo-electron.git
cd uyou-todo-electron
```

2. **安装依赖**
```bash
pnpm install
```

3. **开发运行**
```bash
# Windows
pnpm nr electron:servewin

# macOS
pnpm nr electron:servemac

# Linux
pnpm nr electron:servelinux
```

4. **构建应用**
```bash
# Windows
pnpm nr electron:buildwin

# Windows ARM64
pnpm nr electron:buildwinarm

# macOS/Linux
pnpm nr electron:buildmac
```

### 可用脚本命令

- `pnpm dev` - 启动Vite开发服务器
- `pnpm build` - 构建Vue应用
- `pnpm preview` - 预览构建结果
- `pnpm lint` - 代码检查
- `pnpm lint:fix` - 自动修复代码问题

## 项目特点

1. **现代化技术栈**: 使用Vue 3 + TypeScript + Vite等最新技术
2. **跨平台支持**: 基于Electron，支持Windows、macOS、Linux
3. **丰富的个性化选项**: 主题、字体、界面模式等多种自定义选项
4. **良好的用户体验**: 流畅的动画、直观的操作、美观的界面
5. **完整的功能**: 从基础的待办管理到高级的数据同步和备份
6. **国际化支持**: 多语言界面，适合不同地区用户

这个项目是一个功能完整、界面美观的现代化桌面待办事项应用，充分利用了Vue 3的现代特性和Electron的跨平台能力，为用户提供了丰富的个性化选项和良好的用户体验。
